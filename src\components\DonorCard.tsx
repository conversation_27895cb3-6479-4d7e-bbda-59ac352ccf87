import React from 'react';
import QRCode from 'qrcode.react';
import { MapPin, Phone, Building, Heart } from 'lucide-react';
import { Donor } from '../types/donor';

interface DonorCardProps {
  donor: Donor;
  onDonateClick: () => void;
}

const donationTypeColors = {
  monetary: 'bg-green-100 text-green-800',
  food: 'bg-orange-100 text-orange-800',
  clothing: 'bg-purple-100 text-purple-800',
  medical: 'bg-red-100 text-red-800',
  general: 'bg-blue-100 text-blue-800'
};

const donationTypeLabels = {
  monetary: 'Monetary',
  food: 'Food',
  clothing: 'Clothing',
  medical: 'Medical',
  general: 'General'
};

export default function DonorCard({ donor, onDonateClick }: DonorCardProps) {
  const fullAddress = `${donor.address}, ${donor.city}, ${donor.state} ${donor.zipCode}`;
  const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(fullAddress)}`;

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 animate-fadeIn">
      {/* Header */}
      <div className="bg-gradient-to-r from-pink-500 to-pink-600 px-6 py-4">
        <h2 className="text-xl font-bold text-white">{donor.name}</h2>
        {donor.organization && (
          <div className="flex items-center mt-1">
            <Building className="h-4 w-4 text-pink-100 mr-2" />
            <p className="text-pink-100 font-medium">{donor.organization}</p>
          </div>
        )}
        <div className="mt-2">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${donationTypeColors[donor.donationType]}`}>
            {donationTypeLabels[donor.donationType]} Donations
          </span>
        </div>
      </div>

      <div className="p-6">
        {/* QR Code Section */}
        <div className="flex justify-center mb-6">
          <div className="bg-gray-50 p-4 rounded-xl border-2 border-dashed border-gray-200">
            <QRCode
              value={donor.qrData}
              size={140}
              level="H"
              includeMargin={false}
              className="rounded-lg"
            />
            <p className="text-xs text-gray-500 text-center mt-2">Scan to donate</p>
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <MapPin className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-gray-900 font-medium">Address</p>
              <p className="text-gray-600 text-sm leading-relaxed">{fullAddress}</p>
              <a
                href={googleMapsUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-pink-600 hover:text-pink-800 text-sm font-medium mt-1 transition-colors duration-200"
              >
                View on Google Maps →
              </a>
            </div>
          </div>

          {donor.phone && (
            <div className="flex items-center space-x-3">
              <Phone className="h-5 w-5 text-gray-400 flex-shrink-0" />
              <div>
                <p className="text-gray-900 font-medium">Phone</p>
                <a 
                  href={`tel:${donor.phone}`}
                  className="text-pink-600 hover:text-pink-800 text-sm transition-colors duration-200"
                >
                  {donor.phone}
                </a>
              </div>
            </div>
          )}
        </div>

        {/* Donate Button */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <button
            onClick={onDonateClick}
            className="w-full bg-gradient-to-r from-pink-500 to-rose-500 text-white py-3 px-4 rounded-xl font-semibold hover:from-pink-600 hover:to-rose-600 transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <Heart className="h-5 w-5" />
            <span>Donate Now</span>
          </button>
        </div>
      </div>
    </div>
  );
}